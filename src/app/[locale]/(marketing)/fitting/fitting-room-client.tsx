'use client';

import { LoginWrapper } from '@/components/auth/login-wrapper';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  type TryonRequest,
  type TryonStatusResponse,
  pollTryonTaskStatus,
  saveTryonResult,
  submitTryonTask,
  validateTryonRequest,
} from '@/lib/ai-tryon-client';
import { authClient } from '@/lib/auth-client';
import {
  createCustomModel,
  getCustomModels,
  validateCustomModelData,
} from '@/lib/custom-models-api';
import {
  type FilterOption,
  getBodyTypeOptions,
  getBodyTypeTranslationKey,
  getFilterOptions,
  getFilterTranslationKey,
  getStyleOptions,
  getStyleTranslationKey,
} from '@/lib/model-translations';
import {
  uploadFittingRoomImage,
  uploadModelImage,
} from '@/lib/oss-upload-client';
import {
  type UploadErrorCode,
  categorizeUploadError,
  getErrorTranslationKey,
  validateFile,
  validateRequiredFields,
} from '@/lib/upload-error-handler';
import { cn } from '@/lib/utils';
import { convertCustomModelToFittingModel } from '@/types/custom-model';
import { ImageIcon, Loader2, PlusIcon, Save, UploadIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';
import modelsData from '../../../../../mock/models.json';

interface Model {
  id: string;
  name: string;
  style: string;
  height: string;
  weight: string;
  body: string;
  image: string;
  selected: boolean;
  isCustom: boolean;
  gender: 'female' | 'male';
  description: string;
}

type ClothingType = 'topBottom' | 'onepiece';

export default function FittingRoomClient() {
  const t = useTranslations('FittingRoomPage');
  const { data: session, isPending } = authClient.useSession();
  const [mounted, setMounted] = useState(false);

  const [models, setModels] = useState<Model[]>(modelsData as Model[]);
  const [selectedModel, setSelectedModel] = useState<Model | null>(
    models.find((m) => m.selected) || null
  );
  const [isLoadingCustomModels, setIsLoadingCustomModels] = useState(true);
  const [showModelSelection, setShowModelSelection] = useState(false);
  const [modelFilter, setModelFilter] = useState<FilterOption>('all');
  const [clothingType, setClothingType] = useState<ClothingType>('topBottom');
  const [topImage, setTopImage] = useState<string | null>(null);
  const [bottomImage, setBottomImage] = useState<string | null>(null);
  const [onepieceImage, setOnepieceImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStage, setLoadingStage] = useState<
    'idle' | 'queuing' | 'processing' | 'completed'
  >('idle');
  const [showResult, setShowResult] = useState(false);

  // AI试衣相关状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [tryonResult, setTryonResult] = useState<string | null>(null);
  const [tryonStatus, setTryonStatus] = useState<string>('');
  const [canSave, setCanSave] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Add model dialog state
  const [isAddModelOpen, setIsAddModelOpen] = useState(false);
  const [newModelData, setNewModelData] = useState({
    name: '',
    style: '',
    height: '',
    weight: '',
    bodyType: '',
    image: null as File | null,
  });
  const [isUploading, setIsUploading] = useState(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);

  // 设置组件挂载状态，避免水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 清理新增模特对话框数据
  const resetAddModelDialog = useCallback(() => {
    setNewModelData({
      name: '',
      style: '',
      height: '',
      weight: '',
      bodyType: '',
      image: null,
    });

    // 清理预览URL
    if (imagePreviewUrl) {
      URL.revokeObjectURL(imagePreviewUrl);
      setImagePreviewUrl(null);
    }
  }, [imagePreviewUrl]);

  // Handle URL parameters for images from outfit gallery
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const imageUrl = urlParams.get('image');
    const imageType = urlParams.get('type');
    const topImageUrl = urlParams.get('topImage');
    const bottomImageUrl = urlParams.get('bottomImage');

    // 处理新的参数格式（多件上下装）
    if (topImageUrl || bottomImageUrl) {
      setClothingType('topBottom');
      if (topImageUrl) {
        setTopImage(topImageUrl);
      }
      if (bottomImageUrl) {
        setBottomImage(bottomImageUrl);
      }
      return;
    }

    // 处理原有的单个图片参数格式
    if (imageUrl && imageType) {
      switch (imageType.toLowerCase()) {
        case 'top':
          setTopImage(imageUrl);
          setClothingType('topBottom');
          break;
        case 'bottom':
          setBottomImage(imageUrl);
          setClothingType('topBottom');
          break;
        case 'dress':
        case 'onepiece':
          setOnepieceImage(imageUrl);
          setClothingType('onepiece');
          break;
        case 'topbottom':
          setClothingType('topBottom');
          if (topImageUrl) {
            setTopImage(topImageUrl);
          }
          if (bottomImageUrl) {
            setBottomImage(bottomImageUrl);
          } else {
            // 兼容旧格式，将单个图片设为上装
            setTopImage(imageUrl);
          }
          break;
        default:
          setTopImage(imageUrl);
          setClothingType('topBottom');
      }
    }
  }, []);

  // 加载自定义模特数据
  useEffect(() => {
    const loadCustomModels = async () => {
      try {
        setIsLoadingCustomModels(true);

        // 尝试加载自定义模特数据
        const customModels = await getCustomModels();
        const convertedCustomModels = customModels.map(
          convertCustomModelToFittingModel
        );

        // 将自定义模特放在默认模特前面
        const allModels: Model[] = [
          ...convertedCustomModels,
          ...(modelsData as Model[]),
        ];
        setModels(allModels);

        // 如果当前没有选中的模特，选择第一个模特
        if (!selectedModel && allModels.length > 0) {
          const firstModel = allModels[0];
          setSelectedModel(firstModel);
          setModels((prev) =>
            prev.map((m) => ({ ...m, selected: m.id === firstModel.id }))
          );
        }
      } catch (error) {
        // 如果用户未登录或获取失败，静默处理，只使用默认模特
        // 不显示错误信息，避免在未登录时报错
        setModels(modelsData as Model[]);
      } finally {
        setIsLoadingCustomModels(false);
      }
    };

    loadCustomModels();
  }, []);

  // 组件卸载时清理预览URL
  useEffect(() => {
    return () => {
      if (imagePreviewUrl) {
        URL.revokeObjectURL(imagePreviewUrl);
      }
    };
  }, [imagePreviewUrl]);

  const handleModelSelect = (model: Model) => {
    setSelectedModel(model);
    setModels((prev) =>
      prev.map((m) => ({ ...m, selected: m.id === model.id }))
    );
    setShowModelSelection(false);
  };

  // 筛选模特
  const filteredModels = models.filter((model) => {
    if (modelFilter === 'all') return true;
    if (modelFilter === 'custom') return model.isCustom;
    if (modelFilter === 'female' || modelFilter === 'male') {
      return model.gender === modelFilter;
    }
    return true;
  });

  const handleFileUpload = useCallback(
    async (file: File, type: 'top' | 'bottom' | 'onepiece') => {
      try {
        // 使用新的试衣间图片上传函数（支持 OSS 和存储系统回退）
        const result = await uploadFittingRoomImage(file);

        if (!result.success) {
          throw new Error(result.error || '图片上传失败');
        }

        switch (type) {
          case 'top':
            setTopImage(result.url!);
            break;
          case 'bottom':
            setBottomImage(result.url!);
            break;
          case 'onepiece':
            setOnepieceImage(result.url!);
            break;
        }

        toast.success('图片上传成功');
      } catch (error) {
        console.error('Upload failed:', error);

        // 使用统一的错误处理
        const uploadError = categorizeUploadError(error);
        const errorKey = getErrorTranslationKey(
          uploadError.code as UploadErrorCode
        );
        toast.error(t(errorKey as any));
      }
    },
    [t]
  );

  const handleStartTryOn = async () => {
    // 检查用户是否已登录
    if (!session?.user) {
      // 如果未登录，不显示错误提示，而是通过 LoginWrapper 处理登录
      return;
    }

    if (!selectedModel) {
      toast.error('请先选择模特');
      return;
    }

    if (clothingType === 'topBottom' && (!topImage || !bottomImage)) {
      toast.error('请上传上装和下装图片');
      return;
    }

    if (clothingType === 'onepiece' && !onepieceImage) {
      toast.error('请上传服装图片');
      return;
    }

    // 构建试衣请求
    const tryonRequest: TryonRequest = {
      modelId: selectedModel.isCustom ? selectedModel.id : undefined,
      modelName: selectedModel.name,
      modelImageUrl: selectedModel.image,
      modelType: selectedModel.isCustom ? 'custom' : 'default',
      clothingType,
      topImageUrl: topImage || undefined,
      bottomImageUrl: bottomImage || undefined,
      onepieceImageUrl: onepieceImage || undefined,
    };

    // 验证请求参数
    const validation = validateTryonRequest(tryonRequest);
    if (!validation.valid) {
      toast.error(validation.error || '参数验证失败');
      return;
    }

    // 开始加载流程
    setIsLoading(true);
    setShowResult(true);
    setLoadingStage('queuing');
    setTryonResult(null);
    setCanSave(false);
    setTryonStatus('正在提交试衣任务...');

    try {
      // 提交AI试衣任务
      const submitResult = await submitTryonTask(tryonRequest);

      if (!submitResult.success) {
        throw new Error(submitResult.error || '提交试衣任务失败');
      }

      const taskId = submitResult.taskId!;
      setCurrentTaskId(taskId);
      setTryonStatus('任务已提交，正在排队...');
      setLoadingStage('processing');

      // 开始轮询任务状态
      const finalStatus = await pollTryonTaskStatus(
        taskId,
        (status: TryonStatusResponse) => {
          // 更新状态显示
          if (status.message) {
            setTryonStatus(status.message);
          }

          // 根据任务状态更新加载阶段
          if (
            status.taskStatus === 'RUNNING' ||
            status.taskStatus === 'PRE-PROCESSING'
          ) {
            setLoadingStage('processing');
          }
        }
      );

      if (
        finalStatus.success &&
        finalStatus.taskStatus === 'SUCCEEDED' &&
        finalStatus.imageUrl
      ) {
        // 试衣成功
        setTryonResult(finalStatus.imageUrl);
        setIsLoading(false);
        setLoadingStage('completed');
        setCanSave(true);
        setTryonStatus('试衣完成！');
        toast.success('AI试衣完成！');
      } else {
        // 试衣失败
        throw new Error(finalStatus.error || '试衣失败');
      }
    } catch (error) {
      console.error('AI试衣错误:', error);
      setIsLoading(false);
      setLoadingStage('idle');
      setShowResult(false);
      setTryonStatus('');
      toast.error(error instanceof Error ? error.message : '试衣失败，请重试');
    }
  };

  // 保存试衣结果
  const handleSaveTryonResult = async () => {
    if (!currentTaskId || !tryonResult) {
      toast.error('没有可保存的试衣结果');
      return;
    }

    setIsSaving(true);
    try {
      const saveResult = await saveTryonResult(currentTaskId, tryonResult);

      if (saveResult.success) {
        toast.success(saveResult.message || '试衣结果已保存到相册');
        setCanSave(false); // 保存成功后禁用保存按钮
      } else {
        toast.error(saveResult.error || '保存失败，请重试');
      }
    } catch (error) {
      console.error('保存试衣结果错误:', error);
      toast.error('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddModel = async () => {
    // 使用统一的必填字段验证
    const fieldMap = {
      name: t('modelDialog.name'),
      style: t('modelDialog.style'),
      height: t('modelDialog.height'),
      weight: t('modelDialog.weight'),
      bodyType: t('modelDialog.bodyType'),
      image: t('modelDialog.uploadImage'),
    };

    const validation = validateRequiredFields(
      {
        name: newModelData.name?.trim(),
        style: newModelData.style,
        height: newModelData.height?.trim(),
        weight: newModelData.weight?.trim(),
        bodyType: newModelData.bodyType,
        image: newModelData.image,
      },
      ['name', 'style', 'height', 'weight', 'bodyType', 'image']
    );

    if (!validation.valid && validation.missingFields) {
      const missingFieldNames = validation.missingFields.map(
        (field) => fieldMap[field as keyof typeof fieldMap]
      );
      toast.error(
        t('modelDialog.errors.missingFields', {
          fields: missingFieldNames.join('、'),
        })
      );
      return;
    }

    setIsUploading(true);

    try {
      // 使用新的上传接口（支持 OSS 和存储系统回退）
      const result = await uploadModelImage(newModelData.image!);

      if (!result.success) {
        throw new Error(result.error || '图片上传失败');
      }

      // 保存到数据库
      const customModelData = {
        name: newModelData.name,
        style: newModelData.style,
        height: newModelData.height,
        weight: newModelData.weight,
        bodyType: newModelData.bodyType,
        gender: 'female' as const,
        image: result.url!,
        description: '自定义模特',
      };

      const savedModel = await createCustomModel(customModelData);

      // 转换为试衣间模特格式
      const newModel: Model = convertCustomModelToFittingModel(savedModel);

      // 将新模特添加到列表开头（自定义模特在前面）
      setModels((prev) => [
        newModel,
        ...prev.map((m) => ({ ...m, selected: false })),
      ]);

      // 自动选择新创建的模特
      setSelectedModel(newModel);

      setIsAddModelOpen(false);
      resetAddModelDialog();

      toast.success(t('modelDialog.errors.addModelSuccess'));
    } catch (error) {
      console.error('Failed to add model:', error);

      // 使用统一的错误处理
      const uploadError = categorizeUploadError(error);
      const errorKey = getErrorTranslationKey(
        uploadError.code as UploadErrorCode
      );
      toast.error(t(errorKey as any));
    } finally {
      setIsUploading(false);
    }
  };

  const FileUploadArea = ({
    onFileSelect,
    label,
    image,
  }: {
    onFileSelect: (file: File) => void;
    label: string;
    image: string | null;
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div
        className={cn(
          'border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 transition-colors bg-gray-50/50',
          image && 'border-solid border-green-400 bg-green-50/50'
        )}
        onClick={() => {
          const input = document.createElement('input');
          input.type = 'file';
          input.accept = 'image/*';
          input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) onFileSelect(file);
          };
          input.click();
        }}
      >
        {image ? (
          <div className="relative w-full min-h-24 max-h-32">
            <Image
              src={image}
              alt={label}
              width={200}
              height={120}
              className="w-full h-auto object-contain rounded max-h-32"
            />
            <div className="absolute inset-0 bg-black/20 rounded flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
              <p className="text-white text-xs">点击重新上传</p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-2 py-4">
            <UploadIcon className="w-6 h-6 text-gray-400" />
            <p className="text-xs text-gray-500">点击上传图片</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="container mx-auto p-4 min-h-screen max-w-7xl">
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 min-h-[calc(100vh-12rem)]">
        {/* Left Sidebar */}
        <div className="xl:col-span-1 space-y-6">
          <Card className="h-full">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl">{t('sidebar.title')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 flex-1">
              {/* Model Selection */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    {t('sidebar.modelSection.title')}
                  </Label>
                  {!mounted || isPending ? (
                    <Button variant="outline" size="sm" disabled>
                      <PlusIcon className="w-4 h-4 mr-1" />
                      {t('sidebar.modelSection.addModel')}
                    </Button>
                  ) : session?.user ? (
                    <Dialog
                      open={isAddModelOpen}
                      onOpenChange={setIsAddModelOpen}
                    >
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <PlusIcon className="w-4 h-4 mr-1" />
                          {t('sidebar.modelSection.addModel')}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                          <DialogTitle>{t('modelDialog.title')}</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-6">
                          {/* 上传模特图片 - 移到最上方 */}
                          <div>
                            <Label className="text-sm font-medium">
                              {t('modelDialog.uploadImage')}
                              <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <div
                              className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                              onClick={() => {
                                const input = document.createElement('input');
                                input.type = 'file';
                                input.accept = '.jpg,.jpeg,.png,.webp';
                                input.onchange = (e) => {
                                  const file = (e.target as HTMLInputElement)
                                    .files?.[0];
                                  if (file) {
                                    // 使用统一的文件验证
                                    const validation = validateFile(file);
                                    if (!validation.valid && validation.error) {
                                      const errorKey = getErrorTranslationKey(
                                        validation.error.code as UploadErrorCode
                                      );
                                      toast.error(t(errorKey as any));
                                      return;
                                    }

                                    setNewModelData((prev) => ({
                                      ...prev,
                                      image: file,
                                    }));

                                    // 创建预览URL
                                    const previewUrl =
                                      URL.createObjectURL(file);
                                    setImagePreviewUrl(previewUrl);
                                  }
                                };
                                input.click();
                              }}
                            >
                              {imagePreviewUrl ? (
                                <div className="flex flex-col items-center space-y-2">
                                  <div className="w-24 h-32 rounded-lg overflow-hidden border">
                                    <img
                                      src={imagePreviewUrl}
                                      alt="预览"
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {newModelData.image?.name}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    点击重新选择图片
                                  </div>
                                </div>
                              ) : (
                                <div className="flex flex-col items-center space-y-2">
                                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <svg
                                      className="w-6 h-6 text-gray-400"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                                      />
                                    </svg>
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    点击选择图片
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {t('modelDialog.uploadImageDesc')}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* 模特姓名 */}
                          <div>
                            <Label className="text-sm font-medium">
                              {t('modelDialog.name')}
                              <span className="text-red-500 ml-1">*</span>
                            </Label>
                            <Input
                              className="mt-2"
                              placeholder={t('modelDialog.namePlaceholder')}
                              value={newModelData.name}
                              onChange={(e) =>
                                setNewModelData((prev) => ({
                                  ...prev,
                                  name: e.target.value,
                                }))
                              }
                            />
                          </div>
                          {/* 风格和体型 */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <Label className="text-sm font-medium">
                                {t('modelDialog.style')}
                                <span className="text-red-500 ml-1">*</span>
                              </Label>
                              <Select
                                value={newModelData.style}
                                onValueChange={(value) =>
                                  setNewModelData((prev) => ({
                                    ...prev,
                                    style: value,
                                  }))
                                }
                              >
                                <SelectTrigger className="mt-2 w-full">
                                  <SelectValue
                                    placeholder={t(
                                      'modelDialog.stylePlaceholder'
                                    )}
                                  />
                                </SelectTrigger>
                                <SelectContent className="w-full">
                                  {getStyleOptions().map(({ value, key }) => (
                                    <SelectItem key={value} value={value}>
                                      {t(key as any)}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label className="text-sm font-medium">
                                {t('modelDialog.bodyType')}
                                <span className="text-red-500 ml-1">*</span>
                              </Label>
                              <Select
                                value={newModelData.bodyType}
                                onValueChange={(value) =>
                                  setNewModelData((prev) => ({
                                    ...prev,
                                    bodyType: value,
                                  }))
                                }
                              >
                                <SelectTrigger className="mt-2 w-full">
                                  <SelectValue
                                    placeholder={t(
                                      'modelDialog.bodyTypePlaceholder'
                                    )}
                                  />
                                </SelectTrigger>
                                <SelectContent className="w-full">
                                  {getBodyTypeOptions().map(
                                    ({ value, key }) => (
                                      <SelectItem key={value} value={value}>
                                        {t(key as any)}
                                      </SelectItem>
                                    )
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          {/* 身高和体重 */}
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <Label className="text-sm font-medium">
                                {t('modelDialog.height')}
                                <span className="text-red-500 ml-1">*</span>
                              </Label>
                              <Input
                                className="mt-2 w-full"
                                placeholder={t('modelDialog.heightPlaceholder')}
                                value={newModelData.height}
                                onChange={(e) =>
                                  setNewModelData((prev) => ({
                                    ...prev,
                                    height: e.target.value,
                                  }))
                                }
                              />
                            </div>

                            <div>
                              <Label className="text-sm font-medium">
                                {t('modelDialog.weight')}
                                <span className="text-red-500 ml-1">*</span>
                              </Label>
                              <Input
                                className="mt-2 w-full"
                                placeholder={t('modelDialog.weightPlaceholder')}
                                value={newModelData.weight}
                                onChange={(e) =>
                                  setNewModelData((prev) => ({
                                    ...prev,
                                    weight: e.target.value,
                                  }))
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button
                            variant="outline"
                            onClick={() => {
                              resetAddModelDialog();
                              setIsAddModelOpen(false);
                            }}
                            disabled={isUploading}
                          >
                            {t('modelDialog.cancel')}
                          </Button>
                          <Button
                            onClick={handleAddModel}
                            disabled={isUploading}
                          >
                            {isUploading ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                {t('modelDialog.uploading')}
                              </>
                            ) : (
                              t('modelDialog.confirm')
                            )}
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  ) : (
                    <LoginWrapper mode="modal" asChild>
                      <Button variant="outline" size="sm">
                        <PlusIcon className="w-4 h-4 mr-1" />
                        {t('sidebar.modelSection.addModel')}
                      </Button>
                    </LoginWrapper>
                  )}
                </div>

                {selectedModel ? (
                  <div
                    className="p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
                    onClick={() => setShowModelSelection(true)}
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className="relative w-20 h-36">
                        <Image
                          src={selectedModel.image}
                          alt={selectedModel.name}
                          fill
                          className="object-cover object-top rounded"
                        />
                      </div>
                      <div className="text-center">
                        <p className="font-medium text-sm">
                          {selectedModel.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {t(
                            getStyleTranslationKey(selectedModel.style) as any
                          )}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {selectedModel.height} • {selectedModel.weight}
                        </p>
                        <p className="text-xs text-gray-400">
                          {t(
                            getBodyTypeTranslationKey(selectedModel.body) as any
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div
                    className="flex items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
                    onClick={() => setShowModelSelection(true)}
                  >
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-full flex items-center justify-center">
                        <ImageIcon className="w-6 h-6 text-gray-400" />
                      </div>
                      <p className="text-sm text-gray-500">点击选择模特</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Clothing Type Selection */}
              <div className="space-y-4">
                <Label className="text-base font-medium">
                  {t('sidebar.clothingSection.title')}
                </Label>

                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={
                      clothingType === 'topBottom' ? 'default' : 'outline'
                    }
                    onClick={() => setClothingType('topBottom')}
                    className="h-10 text-sm"
                  >
                    {t('sidebar.clothingSection.topBottom')}
                  </Button>
                  <Button
                    variant={
                      clothingType === 'onepiece' ? 'default' : 'outline'
                    }
                    onClick={() => setClothingType('onepiece')}
                    className="h-10 text-sm"
                  >
                    {t('sidebar.clothingSection.onepiece')}
                  </Button>
                </div>

                {/* File Upload Areas */}
                {clothingType === 'topBottom' ? (
                  <div className="space-y-3">
                    <FileUploadArea
                      label={t('sidebar.clothingSection.uploadTop')}
                      image={topImage}
                      onFileSelect={(file) => handleFileUpload(file, 'top')}
                    />
                    <FileUploadArea
                      label={t('sidebar.clothingSection.uploadBottom')}
                      image={bottomImage}
                      onFileSelect={(file) => handleFileUpload(file, 'bottom')}
                    />
                  </div>
                ) : (
                  <FileUploadArea
                    label={t('sidebar.clothingSection.uploadClothing')}
                    image={onepieceImage}
                    onFileSelect={(file) => handleFileUpload(file, 'onepiece')}
                  />
                )}

                {!mounted || isPending ? (
                  <Button className="w-full h-10" size="default" disabled>
                    {t('sidebar.clothingSection.startTryOn')}
                  </Button>
                ) : session?.user ? (
                  <Button
                    onClick={handleStartTryOn}
                    className="w-full h-10"
                    size="default"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      loadingStage === 'queuing' ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>{t('sidebar.clothingSection.queueing')}</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>{t('sidebar.clothingSection.processing')}</span>
                        </div>
                      )
                    ) : (
                      t('sidebar.clothingSection.startTryOn')
                    )}
                  </Button>
                ) : (
                  <LoginWrapper mode="modal" asChild>
                    <Button className="w-full h-10" size="default">
                      {t('sidebar.clothingSection.startTryOn')}
                    </Button>
                  </LoginWrapper>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Content Area */}
        <div className="xl:col-span-3">
          <Card className="h-full">
            <CardContent
              className={`h-full flex justify-center p-0 ${showModelSelection ? 'items-start' : 'items-center'}`}
            >
              {showModelSelection ? (
                <div className="w-full max-w-6xl p-4 min-h-full">
                  <h3 className="text-2xl font-semibold mb-4 text-center sticky top-0 bg-white z-10 py-2">
                    {t('modelSelection.title')}
                  </h3>

                  {/* 筛选按钮 */}
                  <div className="flex justify-start mb-6">
                    <div className="flex bg-gray-100 rounded-lg p-1">
                      {getFilterOptions().map(({ value, key }) => (
                        <button
                          key={value}
                          type="button"
                          onClick={() => setModelFilter(value)}
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                            modelFilter === value
                              ? 'bg-white text-gray-900 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          {t(key as any)}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 min-h-[300px]">
                    {isLoadingCustomModels ? (
                      <div className="col-span-full flex items-center justify-center py-12">
                        <Loader2 className="w-8 h-8 animate-spin text-purple-500" />
                        <span className="ml-2 text-gray-600">
                          加载模特数据中...
                        </span>
                      </div>
                    ) : filteredModels.length > 0 ? (
                      filteredModels.map((model) => (
                        <div
                          key={model.id}
                          className={cn(
                            'relative border-2 rounded-xl p-6 cursor-pointer transition-all hover:shadow-lg',
                            model.selected
                              ? 'border-purple-500 bg-purple-50'
                              : 'border-gray-200 hover:border-gray-300'
                          )}
                          onClick={() => handleModelSelect(model)}
                        >
                          {/* Custom badge for custom models */}
                          {model.isCustom && (
                            <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full z-10">
                              Custom
                            </div>
                          )}

                          {/* Selection indicator */}
                          {model.selected && (
                            <div className="absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center z-10">
                              <div className="w-2 h-2 bg-white rounded-full" />
                            </div>
                          )}

                          <div className="relative w-full h-64 mb-4 rounded-lg overflow-hidden">
                            <Image
                              src={model.image}
                              alt={model.name}
                              fill
                              className="object-cover object-top"
                            />
                          </div>

                          <div className="text-left space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-semibold text-lg">
                                {model.name}
                              </h4>
                            </div>
                            <p className="text-base text-gray-600">
                              {t(getStyleTranslationKey(model.style) as any)}
                            </p>
                            <div className="text-sm text-gray-500 space-y-2">
                              <div className="flex justify-between">
                                <span>{t('modelSelection.height')}</span>
                                <span>{model.height}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>{t('modelSelection.weight')}</span>
                                <span>{model.weight}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>{t('modelSelection.bodyType')}</span>
                                <span>
                                  {t(
                                    getBodyTypeTranslationKey(model.body) as any
                                  )}
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-400 mt-3">
                              {model.description}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-full flex flex-col items-center justify-center py-12">
                        <div className="text-gray-400 text-center">
                          <ImageIcon className="w-12 h-12 mx-auto mb-4" />
                          <p className="text-lg font-medium mb-2">
                            暂无符合条件的模特
                          </p>
                          <p className="text-sm">请尝试其他筛选条件</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : showResult ? (
                <div className="text-center h-full flex items-center justify-center">
                  {isLoading ? (
                    <div className="space-y-6 relative z-10">
                      {/* 主要加载动画 */}
                      <div className="relative">
                        <div className="w-20 h-20 mx-auto">
                          {/* 脉冲背景 */}
                          <div className="absolute inset-0 bg-purple-100 rounded-full animate-pulse" />

                          {/* 外圈旋转动画 */}
                          <div className="absolute inset-0 border-4 border-purple-200 rounded-full" />
                          <div className="absolute inset-0 border-4 border-purple-500 border-t-transparent rounded-full animate-spin" />

                          {/* 内部图标 */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center animate-pulse">
                              <svg
                                className="w-4 h-4 text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 文字内容 */}
                      <div className="space-y-3">
                        <h3 className="text-xl font-semibold text-gray-800">
                          {loadingStage === 'queuing'
                            ? t('sidebar.clothingSection.loadingTitle')
                            : t('sidebar.clothingSection.processingTitle')}
                        </h3>
                        <p className="text-base text-gray-600">
                          {tryonStatus ||
                            (loadingStage === 'queuing'
                              ? t('sidebar.clothingSection.queueingDesc')
                              : t('sidebar.clothingSection.processingDesc'))}
                        </p>
                      </div>

                      {/* 进度条 */}
                      <div className="w-64 mx-auto">
                        <div className="flex justify-between text-xs text-gray-500 mb-2">
                          <span>
                            {t('sidebar.clothingSection.progressSteps.queuing')}
                          </span>
                          <span>
                            {t(
                              'sidebar.clothingSection.progressSteps.processing'
                            )}
                          </span>
                          <span>
                            {t(
                              'sidebar.clothingSection.progressSteps.completed'
                            )}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-purple-500 h-2 rounded-full transition-all duration-1000 ease-out"
                            style={{
                              width:
                                loadingStage === 'queuing'
                                  ? '33%'
                                  : loadingStage === 'processing'
                                    ? '66%'
                                    : '100%',
                            }}
                          />
                        </div>
                        <p className="text-xs text-gray-400 mt-2">
                          {t('sidebar.clothingSection.estimatedTime')}{' '}
                          {loadingStage === 'queuing' ? '5-10' : '3-5'}{' '}
                          {t('sidebar.clothingSection.seconds')}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {tryonResult ? (
                        <div className="space-y-4">
                          {/* 试衣结果图片 */}
                          <div className="relative w-full max-w-md mx-auto">
                            <img
                              src={tryonResult}
                              alt="AI试衣结果"
                              className="w-full h-auto rounded-lg shadow-lg"
                              style={{
                                maxHeight: '500px',
                                objectFit: 'contain',
                              }}
                            />
                          </div>

                          {/* 成功状态 */}
                          <div className="text-center">
                            <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                              <svg
                                className="w-8 h-8 text-green-500"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                            <p className="text-lg font-medium text-green-600">
                              试衣完成！
                            </p>
                            <p className="text-gray-500 text-sm">
                              {tryonStatus}
                            </p>

                            {/* 保存按钮 */}
                            {canSave && (
                              <Button
                                onClick={handleSaveTryonResult}
                                disabled={isSaving}
                                className="mt-4 bg-purple-600 hover:bg-purple-700 text-white"
                              >
                                {isSaving ? (
                                  <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    保存中...
                                  </>
                                ) : (
                                  <>
                                    <Save className="w-4 h-4 mr-2" />
                                    保存到相册
                                  </>
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
                            <div className="w-8 h-8 bg-green-500 rounded-full" />
                          </div>
                          <p className="text-lg font-medium">试衣完成！</p>
                          <p className="text-gray-500">正在加载试衣结果...</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 p-12">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <ImageIcon className="w-12 h-12 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-700 mb-2">
                    AI试衣间
                  </h3>
                  <p className="text-sm text-gray-500">
                    选择模特和服装开始体验AI试衣
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
